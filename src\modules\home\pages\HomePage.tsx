import React from 'react';
import { useTranslation } from 'react-i18next';

import ModuleCard from '@/modules/components/card/ModuleCard';
import { PageWrapper, ResponsiveGrid, Typography } from '@/shared/components/common';

/**
 * Trang chủ hiển thị các module ch<PERSON>h của hệ thống
 */
const HomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'home']);

  // Danh sách các module chính
  const modules = [
    {
      id: 'okrs',
      title: 'OKRs',
      description: t('home:modules.okrs.description', 'Quản lý mục tiêu và kết quả then chốt'),
      icon: 'okr',
      count: 5,
      countLabel: t('home:modules.okrs.countLabel', 'Mục tiêu đang theo dõi'),
      linkTo: '/okrs',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'todolist',
      title: 'Todolist',
      description: t('home:modules.todolist.description', 'Quản lý công việc và dự án'),
      icon: 'check',
      count: 12,
      countLabel: t('home:modules.todolist.countLabel', 'Công việc đang thực hiện'),
      linkTo: '/todolist',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'calendar',
      title: t('home:modules.calendar.title', 'Lịch'),
      description: t('home:modules.calendar.description', 'Quản lý lịch làm việc và sự kiện'),
      icon: 'calendar',
      count: 8,
      countLabel: t('home:modules.calendar.countLabel', 'Sự kiện sắp tới'),
      linkTo: '/calendar',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'marketing',
      title: 'Marketing',
      description: t(
        'home:modules.marketing.description',
        'Quản lý chiến dịch và nội dung marketing'
      ),
      icon: 'campaign',
      count: 3,
      countLabel: t('home:modules.marketing.countLabel', 'Chiến dịch đang hoạt động'),
      linkTo: '/marketing',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'crm',
      title: 'CRM',
      description: t('home:modules.crm.description', 'Quản lý khách hàng và mối quan hệ'),
      icon: 'users',
      count: 150,
      countLabel: t('home:modules.crm.countLabel', 'Khách hàng'),
      linkTo: '/crm',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'hrm',
      title: 'HRM',
      description: t('home:modules.hrm.description', 'Quản lý nhân sự và tuyển dụng'),
      icon: 'user',
      count: 25,
      countLabel: t('home:modules.hrm.countLabel', 'Nhân viên'),
      linkTo: '/hrm',
      linkText: t('common:view', 'Xem'),
    },
  ];

  return (
    <PageWrapper>
      <div className="mb-8">
        <Typography variant="h3" className="mb-2">
          {t('home:welcome', 'Chào mừng đến với RedAI')}
        </Typography>
        <Typography variant="body1" color="muted">
          {t('home:welcomeDescription', 'Hệ thống quản lý doanh nghiệp toàn diện')}
        </Typography>
      </div>

      <ResponsiveGrid
        gap={4}
        maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 4 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 4 }}
      >
        {modules.map(module => (
          <ModuleCard
            key={module.id}
            title={module.title}
            description={module.description}
            icon={module.icon}
            linkTo={module.linkTo}
            className="h-full"
          />
        ))}
      </ResponsiveGrid>
    </PageWrapper>
  );
};

export default HomePage;
